import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/subscriptions/ui/utils/subscription_helpers.dart';

class SubscriptionSuccessDialog extends StatelessWidget {
  const SubscriptionSuccessDialog({
    super.key,
    required this.tierName,
  });

  final String tierName;

  @override
  Widget build(BuildContext context) {
    final displayTierName = SubscriptionHelpers.capitalizeTierName(tierName);

    return Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.symmetric(horizontal: 24),
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: AppColors.surfacePrimary,
              boxShadow: [
                BoxShadow(
                  color: AppColors.dropShadow.withAlpha(30),
                  spreadRadius: -4,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Success icon with gradient background
                SizedBox(
                  width: 80,
                  height: 80,
                  child: Center(
                    child: Assets.svgs.logo.svg(
                      width: 80,
                      height: 80,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Subtitle
                Text(
                  context.L.subscriptionSuccessSubtitle,
                  style: TextStyles.body2.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Main title
                Text(
                  context.L.subscriptionSuccessTitle(displayTierName),
                  style: TextStyles.heading3.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Description
                Text(
                  context.L.subscriptionSuccessMessage,
                  style: TextStyles.body1.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Action button
                SizedBox(
                  width: double.infinity,
                  child: PrimaryButton(
                    onPressed: () => NavService.popDialog(context),
                    child: Text(context.L.subscriptionSuccessButton),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
